#!/usr/bin/env python3
"""
系统资源监控和性能分析工具
实时监控CPU、内存、磁盘、网络等系统资源
"""

import time
import json
import psutil
import threading
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import argparse

@dataclass
class SystemSnapshot:
    """系统快照数据类"""
    timestamp: str
    cpu_percent: float
    cpu_count: int
    memory_total_gb: float
    memory_used_gb: float
    memory_percent: float
    disk_total_gb: float
    disk_used_gb: float
    disk_percent: float
    disk_read_mb_per_sec: float
    disk_write_mb_per_sec: float
    network_sent_mb_per_sec: float
    network_recv_mb_per_sec: float
    process_count: int
    load_average: List[float]

@dataclass
class ProcessInfo:
    """进程信息数据类"""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    create_time: str

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, interval: float = 1.0):
        """
        初始化系统监控器
        
        Args:
            interval: 监控间隔（秒）
        """
        self.interval = interval
        self.monitoring = False
        self.snapshots: List[SystemSnapshot] = []
        self.monitor_thread = None
        self.start_time = None
        
        # 初始IO统计
        self.last_disk_io = None
        self.last_network_io = None
        self.last_timestamp = None
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n收到信号 {signum}，正在停止监控...")
        self.stop_monitoring()
        sys.exit(0)
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            print("监控已在运行中")
            return
        
        print(f"开始系统监控，间隔: {self.interval}秒")
        self.monitoring = True
        self.start_time = datetime.now()
        
        # 获取初始IO统计
        self.last_disk_io = psutil.disk_io_counters()
        self.last_network_io = psutil.net_io_counters()
        self.last_timestamp = time.time()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
        
        print("停止系统监控")
        self.monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=5)
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                snapshot = self._take_snapshot()
                self.snapshots.append(snapshot)
                
                # 实时输出（可选）
                if len(self.snapshots) % 10 == 0:  # 每10次输出一次
                    self._print_current_status(snapshot)
                
            except Exception as e:
                print(f"监控错误: {e}")
            
            time.sleep(self.interval)
    
    def _take_snapshot(self) -> SystemSnapshot:
        """获取系统快照"""
        current_time = time.time()
        timestamp = datetime.now().isoformat()
        
        # CPU信息
        cpu_percent = psutil.cpu_percent()
        cpu_count = psutil.cpu_count()
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_total_gb = memory.total / (1024**3)
        memory_used_gb = memory.used / (1024**3)
        memory_percent = memory.percent
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_total_gb = disk.total / (1024**3)
        disk_used_gb = disk.used / (1024**3)
        disk_percent = (disk.used / disk.total) * 100
        
        # 磁盘IO速率
        disk_read_mb_per_sec = 0
        disk_write_mb_per_sec = 0
        current_disk_io = psutil.disk_io_counters()
        if self.last_disk_io and current_disk_io:
            time_delta = current_time - self.last_timestamp
            if time_delta > 0:
                read_bytes_delta = current_disk_io.read_bytes - self.last_disk_io.read_bytes
                write_bytes_delta = current_disk_io.write_bytes - self.last_disk_io.write_bytes
                disk_read_mb_per_sec = (read_bytes_delta / time_delta) / (1024**2)
                disk_write_mb_per_sec = (write_bytes_delta / time_delta) / (1024**2)
        
        # 网络IO速率
        network_sent_mb_per_sec = 0
        network_recv_mb_per_sec = 0
        current_network_io = psutil.net_io_counters()
        if self.last_network_io and current_network_io:
            time_delta = current_time - self.last_timestamp
            if time_delta > 0:
                sent_bytes_delta = current_network_io.bytes_sent - self.last_network_io.bytes_sent
                recv_bytes_delta = current_network_io.bytes_recv - self.last_network_io.bytes_recv
                network_sent_mb_per_sec = (sent_bytes_delta / time_delta) / (1024**2)
                network_recv_mb_per_sec = (recv_bytes_delta / time_delta) / (1024**2)
        
        # 进程数量
        process_count = len(psutil.pids())
        
        # 系统负载（仅Unix系统）
        load_average = []
        try:
            load_average = list(psutil.getloadavg())
        except AttributeError:
            # Windows系统不支持getloadavg
            load_average = [0.0, 0.0, 0.0]
        
        # 更新上次记录
        self.last_disk_io = current_disk_io
        self.last_network_io = current_network_io
        self.last_timestamp = current_time
        
        return SystemSnapshot(
            timestamp=timestamp,
            cpu_percent=cpu_percent,
            cpu_count=cpu_count,
            memory_total_gb=memory_total_gb,
            memory_used_gb=memory_used_gb,
            memory_percent=memory_percent,
            disk_total_gb=disk_total_gb,
            disk_used_gb=disk_used_gb,
            disk_percent=disk_percent,
            disk_read_mb_per_sec=disk_read_mb_per_sec,
            disk_write_mb_per_sec=disk_write_mb_per_sec,
            network_sent_mb_per_sec=network_sent_mb_per_sec,
            network_recv_mb_per_sec=network_recv_mb_per_sec,
            process_count=process_count,
            load_average=load_average
        )
    
    def _print_current_status(self, snapshot: SystemSnapshot):
        """打印当前状态"""
        print(f"\n=== 系统状态 {snapshot.timestamp} ===")
        print(f"CPU: {snapshot.cpu_percent:.1f}% ({snapshot.cpu_count} 核)")
        print(f"内存: {snapshot.memory_used_gb:.1f}GB / {snapshot.memory_total_gb:.1f}GB ({snapshot.memory_percent:.1f}%)")
        print(f"磁盘: {snapshot.disk_used_gb:.1f}GB / {snapshot.disk_total_gb:.1f}GB ({snapshot.disk_percent:.1f}%)")
        print(f"磁盘IO: 读 {snapshot.disk_read_mb_per_sec:.2f}MB/s, 写 {snapshot.disk_write_mb_per_sec:.2f}MB/s")
        print(f"网络IO: 发送 {snapshot.network_sent_mb_per_sec:.2f}MB/s, 接收 {snapshot.network_recv_mb_per_sec:.2f}MB/s")
        print(f"进程数: {snapshot.process_count}")
        print(f"负载: {snapshot.load_average}")
    
    def get_top_processes(self, limit: int = 10, sort_by: str = "cpu") -> List[ProcessInfo]:
        """获取资源占用最高的进程"""
        processes = []
        
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'memory_info', 'status', 'create_time']):
            try:
                info = proc.info
                processes.append(ProcessInfo(
                    pid=info['pid'],
                    name=info['name'],
                    cpu_percent=info['cpu_percent'] or 0,
                    memory_percent=info['memory_percent'] or 0,
                    memory_mb=(info['memory_info'].rss / (1024**2)) if info['memory_info'] else 0,
                    status=info['status'],
                    create_time=datetime.fromtimestamp(info['create_time']).isoformat() if info['create_time'] else ""
                ))
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
        
        # 排序
        if sort_by == "cpu":
            processes.sort(key=lambda x: x.cpu_percent, reverse=True)
        elif sort_by == "memory":
            processes.sort(key=lambda x: x.memory_percent, reverse=True)
        
        return processes[:limit]
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成监控摘要"""
        if not self.snapshots:
            return {"error": "没有监控数据"}
        
        # 计算统计信息
        cpu_values = [s.cpu_percent for s in self.snapshots]
        memory_values = [s.memory_percent for s in self.snapshots]
        disk_values = [s.disk_percent for s in self.snapshots]
        
        summary = {
            "monitoring_period": {
                "start_time": self.snapshots[0].timestamp,
                "end_time": self.snapshots[-1].timestamp,
                "duration_minutes": len(self.snapshots) * self.interval / 60,
                "sample_count": len(self.snapshots)
            },
            "cpu": {
                "avg_percent": sum(cpu_values) / len(cpu_values),
                "max_percent": max(cpu_values),
                "min_percent": min(cpu_values),
                "core_count": self.snapshots[0].cpu_count
            },
            "memory": {
                "avg_percent": sum(memory_values) / len(memory_values),
                "max_percent": max(memory_values),
                "min_percent": min(memory_values),
                "total_gb": self.snapshots[0].memory_total_gb
            },
            "disk": {
                "avg_percent": sum(disk_values) / len(disk_values),
                "max_percent": max(disk_values),
                "min_percent": min(disk_values),
                "total_gb": self.snapshots[0].disk_total_gb
            },
            "io_stats": {
                "max_disk_read_mb_per_sec": max(s.disk_read_mb_per_sec for s in self.snapshots),
                "max_disk_write_mb_per_sec": max(s.disk_write_mb_per_sec for s in self.snapshots),
                "max_network_sent_mb_per_sec": max(s.network_sent_mb_per_sec for s in self.snapshots),
                "max_network_recv_mb_per_sec": max(s.network_recv_mb_per_sec for s in self.snapshots)
            }
        }
        
        return summary
    
    def save_data(self, output_dir: str, prefix: str = "system_monitor"):
        """保存监控数据"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存原始数据
        raw_file = output_path / f"{prefix}_{timestamp}_raw.json"
        with open(raw_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(s) for s in self.snapshots], f, indent=2, ensure_ascii=False)
        
        # 保存摘要
        summary = self.generate_summary()
        summary_file = output_path / f"{prefix}_{timestamp}_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        # 保存进程信息
        top_processes = self.get_top_processes(20)
        process_file = output_path / f"{prefix}_{timestamp}_processes.json"
        with open(process_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(p) for p in top_processes], f, indent=2, ensure_ascii=False)
        
        print(f"监控数据已保存到: {output_path}")
        return {
            "raw_data": str(raw_file),
            "summary": str(summary_file),
            "processes": str(process_file)
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="系统资源监控工具")
    parser.add_argument("--interval", type=float, default=1.0, help="监控间隔（秒）")
    parser.add_argument("--duration", type=int, help="监控持续时间（秒）")
    parser.add_argument("--output", default="monitoring_results", help="输出目录")
    parser.add_argument("--prefix", default="system_monitor", help="文件名前缀")
    parser.add_argument("--quiet", action="store_true", help="静默模式，不输出实时状态")
    parser.add_argument("--processes", action="store_true", help="显示进程信息")
    parser.add_argument("--summary-only", action="store_true", help="只显示摘要信息")

    args = parser.parse_args()

    # 创建监控器
    monitor = SystemMonitor(interval=args.interval)

    if args.summary_only:
        # 只显示当前系统状态摘要
        snapshot = monitor._take_snapshot()
        monitor._print_current_status(snapshot)

        if args.processes:
            print("\n=== TOP 进程 (按CPU使用率) ===")
            top_processes = monitor.get_top_processes(10, "cpu")
            for i, proc in enumerate(top_processes, 1):
                print(f"{i:2d}. PID:{proc.pid:6d} {proc.name:20s} CPU:{proc.cpu_percent:5.1f}% MEM:{proc.memory_percent:5.1f}% ({proc.memory_mb:6.1f}MB)")

        return

    try:
        # 开始监控
        monitor.start_monitoring()

        if args.duration:
            # 指定持续时间
            print(f"监控将持续 {args.duration} 秒...")
            time.sleep(args.duration)
        else:
            # 手动停止
            print("按 Ctrl+C 停止监控...")
            try:
                while monitor.monitoring:
                    time.sleep(1)
            except KeyboardInterrupt:
                pass

    finally:
        # 停止监控并保存数据
        monitor.stop_monitoring()

        if monitor.snapshots:
            files = monitor.save_data(args.output, args.prefix)

            # 显示摘要
            summary = monitor.generate_summary()
            print(f"\n=== 监控摘要 ===")
            print(f"监控时长: {summary['monitoring_period']['duration_minutes']:.1f} 分钟")
            print(f"采样次数: {summary['monitoring_period']['sample_count']}")
            print(f"CPU使用率: 平均 {summary['cpu']['avg_percent']:.1f}%, 最高 {summary['cpu']['max_percent']:.1f}%")
            print(f"内存使用率: 平均 {summary['memory']['avg_percent']:.1f}%, 最高 {summary['memory']['max_percent']:.1f}%")
            print(f"磁盘使用率: 平均 {summary['disk']['avg_percent']:.1f}%, 最高 {summary['disk']['max_percent']:.1f}%")
            print(f"最大磁盘读取: {summary['io_stats']['max_disk_read_mb_per_sec']:.2f} MB/s")
            print(f"最大磁盘写入: {summary['io_stats']['max_disk_write_mb_per_sec']:.2f} MB/s")
            print(f"最大网络发送: {summary['io_stats']['max_network_sent_mb_per_sec']:.2f} MB/s")
            print(f"最大网络接收: {summary['io_stats']['max_network_recv_mb_per_sec']:.2f} MB/s")

            print(f"\n数据文件:")
            for key, file_path in files.items():
                print(f"  {key}: {file_path}")
        else:
            print("没有收集到监控数据")

if __name__ == "__main__":
    main()
