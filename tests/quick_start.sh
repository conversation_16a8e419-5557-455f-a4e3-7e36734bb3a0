#!/bin/bash

# API性能测试工具快速启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python() {
    print_info "检查Python环境..."
    
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 未安装，请先安装Python3"
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_success "Python版本: $python_version"
}

# 安装依赖
install_dependencies() {
    print_info "安装依赖包..."
    
    if [ -f "tests/requirements.txt" ]; then
        pip3 install -r tests/requirements.txt
        print_success "依赖安装完成"
    else
        print_warning "requirements.txt 文件不存在，手动安装主要依赖..."
        pip3 install locust aiohttp psutil pandas matplotlib seaborn
    fi
}

# 检查API服务
check_api_service() {
    local api_url=${1:-"http://localhost:8080/api/v1"}
    print_info "检查API服务状态: $api_url"
    
    if curl -s "$api_url/health" > /dev/null; then
        print_success "API服务正常运行"
        return 0
    else
        print_warning "API服务未运行或不可访问"
        return 1
    fi
}

# 运行冒烟测试
run_smoke_test() {
    print_info "运行冒烟测试..."
    cd tests
    python3 load_test/run_load_test.py run smoke --environment local
    print_success "冒烟测试完成"
}

# 运行负载测试
run_load_test() {
    print_info "运行负载测试..."
    cd tests
    python3 load_test/run_load_test.py run load --environment local
    print_success "负载测试完成"
}

# 运行性能测试
run_performance_test() {
    print_info "运行性能测试..."
    cd tests
    python3 performance/performance_test.py --config quick --output results
    print_success "性能测试完成"
}

# 运行综合测试
run_comprehensive_test() {
    local duration=${1:-300}
    local concurrency=${2:-20}
    
    print_info "运行综合测试 (时长: ${duration}s, 并发: ${concurrency})"
    cd tests
    python3 run_comprehensive_test.py --duration $duration --concurrency $concurrency
    print_success "综合测试完成"
}

# 生成示例报告
generate_sample_report() {
    print_info "生成示例报告..."
    cd tests
    python3 reports/report_template.py --sample --output sample_report.html
    print_success "示例报告已生成: tests/sample_report.html"
}

# 显示帮助信息
show_help() {
    echo "API性能测试工具快速启动脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  setup                    - 安装依赖和环境检查"
    echo "  check                    - 检查API服务状态"
    echo "  smoke                    - 运行冒烟测试"
    echo "  load                     - 运行负载测试"
    echo "  performance              - 运行性能测试"
    echo "  comprehensive [时长] [并发] - 运行综合测试"
    echo "  report                   - 生成示例报告"
    echo "  all                      - 运行完整测试流程"
    echo "  help                     - 显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 setup                 # 安装依赖"
    echo "  $0 smoke                 # 快速冒烟测试"
    echo "  $0 comprehensive 600 50  # 10分钟，50并发"
    echo "  $0 all                   # 完整测试流程"
}

# 运行完整测试流程
run_all_tests() {
    print_info "开始完整测试流程..."
    
    # 1. 环境检查
    check_python
    
    # 2. 检查API服务
    if ! check_api_service; then
        print_error "API服务不可用，请先启动API服务"
        exit 1
    fi
    
    # 3. 运行冒烟测试
    run_smoke_test
    
    # 4. 运行性能测试
    run_performance_test
    
    # 5. 生成报告
    generate_sample_report
    
    print_success "完整测试流程完成！"
    print_info "查看结果文件:"
    echo "  - 测试结果: tests/results/"
    echo "  - 示例报告: tests/sample_report.html"
}

# 主函数
main() {
    case "${1:-help}" in
        "setup")
            check_python
            install_dependencies
            ;;
        "check")
            check_api_service "${2:-http://localhost:8080/api/v1}"
            ;;
        "smoke")
            check_python
            run_smoke_test
            ;;
        "load")
            check_python
            run_load_test
            ;;
        "performance")
            check_python
            run_performance_test
            ;;
        "comprehensive")
            check_python
            run_comprehensive_test "${2:-300}" "${3:-20}"
            ;;
        "report")
            check_python
            generate_sample_report
            ;;
        "all")
            run_all_tests
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
main "$@"
