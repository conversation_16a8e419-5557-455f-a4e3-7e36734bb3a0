#!/usr/bin/env python3
"""
压测报告模板生成器
生成HTML格式的压测报告
"""

import json
import base64
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import argparse

class ReportTemplate:
    """报告模板生成器"""
    
    def __init__(self):
        self.template = self._get_html_template()
    
    def generate_report(self, 
                       test_summary: Dict[str, Any],
                       performance_analysis: Dict[str, Any],
                       system_analysis: Optional[Dict[str, Any]] = None,
                       charts_dir: Optional[str] = None,
                       output_file: str = "performance_report.html"):
        """
        生成完整的性能测试报告
        
        Args:
            test_summary: 测试摘要数据
            performance_analysis: 性能分析数据
            system_analysis: 系统分析数据
            charts_dir: 图表目录
            output_file: 输出文件路径
        """
        
        # 准备报告数据
        report_data = {
            "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "test_summary": test_summary,
            "performance_analysis": performance_analysis,
            "system_analysis": system_analysis or {},
            "charts": self._load_charts(charts_dir) if charts_dir else {}
        }
        
        # 生成HTML内容
        html_content = self._render_template(report_data)
        
        # 保存文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"性能测试报告已生成: {output_file}")
        return output_file
    
    def _load_charts(self, charts_dir: str) -> Dict[str, str]:
        """加载图表文件并转换为base64"""
        charts = {}
        charts_path = Path(charts_dir)
        
        if not charts_path.exists():
            return charts
        
        chart_files = {
            "response_time_analysis": "response_time_analysis.png",
            "response_time_timeline": "response_time_timeline.png", 
            "throughput_timeline": "throughput_timeline.png",
            "error_rate_by_endpoint": "error_rate_by_endpoint.png",
            "system_resources": "system_resources.png"
        }
        
        for chart_name, filename in chart_files.items():
            chart_path = charts_path / filename
            if chart_path.exists():
                with open(chart_path, 'rb') as f:
                    chart_data = base64.b64encode(f.read()).decode('utf-8')
                    charts[chart_name] = f"data:image/png;base64,{chart_data}"
        
        return charts
    
    def _render_template(self, data: Dict[str, Any]) -> str:
        """渲染HTML模板"""
        html = self.template
        
        # 替换基本信息
        html = html.replace("{{GENERATED_AT}}", data["generated_at"])
        html = html.replace("{{TEST_NAME}}", data["test_summary"].get("test_name", "性能测试"))
        
        # 替换测试摘要
        summary = data["test_summary"]
        html = html.replace("{{TOTAL_REQUESTS}}", str(summary.get("total_requests", 0)))
        html = html.replace("{{SUCCESSFUL_REQUESTS}}", str(summary.get("successful_requests", 0)))
        html = html.replace("{{FAILED_REQUESTS}}", str(summary.get("failed_requests", 0)))
        html = html.replace("{{ERROR_RATE}}", f"{summary.get('error_rate', 0)*100:.2f}%")
        html = html.replace("{{AVG_RESPONSE_TIME}}", f"{summary.get('avg_response_time', 0):.2f}ms")
        html = html.replace("{{P95_RESPONSE_TIME}}", f"{summary.get('p95_response_time', 0):.2f}ms")
        html = html.replace("{{P99_RESPONSE_TIME}}", f"{summary.get('p99_response_time', 0):.2f}ms")
        html = html.replace("{{REQUESTS_PER_SECOND}}", f"{summary.get('requests_per_second', 0):.2f}")
        html = html.replace("{{DURATION}}", f"{summary.get('duration_seconds', 0):.1f}秒")
        
        # 替换端点分析表格
        endpoint_rows = self._generate_endpoint_table(data["performance_analysis"])
        html = html.replace("{{ENDPOINT_ANALYSIS_TABLE}}", endpoint_rows)
        
        # 替换系统资源信息
        if data["system_analysis"]:
            sys_info = self._generate_system_info(data["system_analysis"])
            html = html.replace("{{SYSTEM_ANALYSIS}}", sys_info)
        else:
            html = html.replace("{{SYSTEM_ANALYSIS}}", "<p>无系统监控数据</p>")
        
        # 替换图表
        charts = data["charts"]
        for chart_name, chart_data in charts.items():
            placeholder = f"{{{{{chart_name.upper()}_CHART}}}}"
            if chart_data:
                img_tag = f'<img src="{chart_data}" alt="{chart_name}" style="max-width: 100%; height: auto;">'
                html = html.replace(placeholder, img_tag)
            else:
                html = html.replace(placeholder, "<p>图表不可用</p>")
        
        # 清理未替换的占位符
        import re
        html = re.sub(r'\{\{[^}]+\}\}', '<span style="color: red;">数据不可用</span>', html)
        
        return html
    
    def _generate_endpoint_table(self, performance_analysis: Dict[str, Any]) -> str:
        """生成端点分析表格"""
        if not performance_analysis:
            return "<tr><td colspan='7'>无性能数据</td></tr>"
        
        rows = []
        for endpoint, stats in performance_analysis.items():
            row = f"""
            <tr>
                <td>{endpoint}</td>
                <td>{stats.get('total_requests', 0)}</td>
                <td>{stats.get('successful_requests', 0)}</td>
                <td>{stats.get('error_rate', 0)*100:.2f}%</td>
                <td>{stats.get('avg_response_time', 0):.2f}ms</td>
                <td>{stats.get('p95_response_time', 0):.2f}ms</td>
                <td>{stats.get('p99_response_time', 0):.2f}ms</td>
            </tr>
            """
            rows.append(row)
        
        return "".join(rows)
    
    def _generate_system_info(self, system_analysis: Dict[str, Any]) -> str:
        """生成系统资源信息"""
        cpu = system_analysis.get('cpu', {})
        memory = system_analysis.get('memory', {})
        disk = system_analysis.get('disk', {})
        
        return f"""
        <div class="system-metrics">
            <div class="metric-card">
                <h4>CPU使用率</h4>
                <p>平均: {cpu.get('avg', 0):.1f}%</p>
                <p>最大: {cpu.get('max', 0):.1f}%</p>
                <p>标准差: {cpu.get('std', 0):.1f}%</p>
            </div>
            <div class="metric-card">
                <h4>内存使用率</h4>
                <p>平均: {memory.get('avg', 0):.1f}%</p>
                <p>最大: {memory.get('max', 0):.1f}%</p>
                <p>标准差: {memory.get('std', 0):.1f}%</p>
            </div>
            <div class="metric-card">
                <h4>磁盘使用率</h4>
                <p>平均: {disk.get('avg', 0):.1f}%</p>
                <p>最大: {disk.get('max', 0):.1f}%</p>
                <p>标准差: {disk.get('std', 0):.1f}%</p>
            </div>
        </div>
        """
    
    def _get_html_template(self) -> str:
        """获取HTML模板"""
        return """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{TEST_NAME}} - 性能测试报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .summary-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .summary-card h3 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #333;
        }
        
        .summary-card .unit {
            font-size: 0.9em;
            color: #666;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        th {
            background-color: #667eea;
            color: white;
            font-weight: 600;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .chart-container {
            margin: 20px 0;
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .chart-container h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        .system-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #28a745;
        }
        
        .metric-card h4 {
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .metric-card p {
            margin: 5px 0;
            color: #666;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .summary-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{TEST_NAME}}</h1>
            <p>性能测试报告 - 生成时间: {{GENERATED_AT}}</p>
        </div>
        
        <div class="summary-grid">
            <div class="summary-card">
                <h3>总请求数</h3>
                <div class="value">{{TOTAL_REQUESTS}}</div>
            </div>
            <div class="summary-card">
                <h3>成功请求数</h3>
                <div class="value">{{SUCCESSFUL_REQUESTS}}</div>
            </div>
            <div class="summary-card">
                <h3>失败请求数</h3>
                <div class="value">{{FAILED_REQUESTS}}</div>
            </div>
            <div class="summary-card">
                <h3>错误率</h3>
                <div class="value">{{ERROR_RATE}}</div>
            </div>
            <div class="summary-card">
                <h3>平均响应时间</h3>
                <div class="value">{{AVG_RESPONSE_TIME}}</div>
            </div>
            <div class="summary-card">
                <h3>P95响应时间</h3>
                <div class="value">{{P95_RESPONSE_TIME}}</div>
            </div>
            <div class="summary-card">
                <h3>P99响应时间</h3>
                <div class="value">{{P99_RESPONSE_TIME}}</div>
            </div>
            <div class="summary-card">
                <h3>吞吐量</h3>
                <div class="value">{{REQUESTS_PER_SECOND}} <span class="unit">RPS</span></div>
            </div>
            <div class="summary-card">
                <h3>测试时长</h3>
                <div class="value">{{DURATION}}</div>
            </div>
        </div>""" + self.get_template_continuation()

    def get_template_continuation(self) -> str:
        """获取模板的后半部分"""
        return """
        <div class="section">
            <h2>端点性能分析</h2>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>端点</th>
                            <th>总请求数</th>
                            <th>成功请求数</th>
                            <th>错误率</th>
                            <th>平均响应时间</th>
                            <th>P95响应时间</th>
                            <th>P99响应时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{ENDPOINT_ANALYSIS_TABLE}}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="section">
            <h2>响应时间分析</h2>
            <div class="chart-container">
                <h3>响应时间分布与统计</h3>
                {{RESPONSE_TIME_ANALYSIS_CHART}}
            </div>
            <div class="chart-container">
                <h3>响应时间时间线</h3>
                {{RESPONSE_TIME_TIMELINE_CHART}}
            </div>
        </div>
        
        <div class="section">
            <h2>吞吐量分析</h2>
            <div class="chart-container">
                <h3>吞吐量时间线</h3>
                {{THROUGHPUT_TIMELINE_CHART}}
            </div>
        </div>
        
        <div class="section">
            <h2>错误率分析</h2>
            <div class="chart-container">
                <h3>各端点错误率</h3>
                {{ERROR_RATE_BY_ENDPOINT_CHART}}
            </div>
        </div>
        
        <div class="section">
            <h2>系统资源使用情况</h2>
            {{SYSTEM_ANALYSIS}}
            <div class="chart-container">
                <h3>系统资源监控</h3>
                {{SYSTEM_RESOURCES_CHART}}
            </div>
        </div>
        
        <div class="footer">
            <p>此报告由性能测试工具自动生成</p>
            <p>如有疑问，请联系测试团队</p>
        </div>
    </div>
</body>
</html>
        """

def generate_sample_data():
    """生成示例数据用于演示"""
    test_summary = {
        "test_name": "API压力测试",
        "start_time": "2024-01-15T10:00:00",
        "end_time": "2024-01-15T10:30:00",
        "duration_seconds": 1800,
        "total_requests": 15000,
        "successful_requests": 14250,
        "failed_requests": 750,
        "error_rate": 0.05,
        "avg_response_time": 245.6,
        "min_response_time": 89.2,
        "max_response_time": 2340.8,
        "p50_response_time": 198.4,
        "p95_response_time": 456.7,
        "p99_response_time": 892.3,
        "requests_per_second": 8.33,
        "total_data_sent_mb": 125.6,
        "total_data_received_mb": 890.4
    }

    performance_analysis = {
        "/llm-qa": {
            "total_requests": 5000,
            "successful_requests": 4800,
            "error_rate": 0.04,
            "avg_response_time": 320.5,
            "median_response_time": 280.3,
            "min_response_time": 150.2,
            "max_response_time": 1200.8,
            "p95_response_time": 580.4,
            "p99_response_time": 890.2,
            "std_response_time": 125.6
        },
        "/rag-qa": {
            "total_requests": 4000,
            "successful_requests": 3850,
            "error_rate": 0.0375,
            "avg_response_time": 280.3,
            "median_response_time": 245.7,
            "min_response_time": 120.5,
            "max_response_time": 980.4,
            "p95_response_time": 520.8,
            "p99_response_time": 750.3,
            "std_response_time": 98.7
        },
        "/data-qa": {
            "total_requests": 3500,
            "successful_requests": 3350,
            "error_rate": 0.043,
            "avg_response_time": 195.8,
            "median_response_time": 175.2,
            "min_response_time": 89.2,
            "max_response_time": 650.4,
            "p95_response_time": 380.5,
            "p99_response_time": 520.7,
            "std_response_time": 78.9
        },
        "/search": {
            "total_requests": 2500,
            "successful_requests": 2450,
            "error_rate": 0.02,
            "avg_response_time": 125.4,
            "median_response_time": 110.8,
            "min_response_time": 45.6,
            "max_response_time": 380.2,
            "p95_response_time": 220.3,
            "p99_response_time": 290.5,
            "std_response_time": 45.2
        }
    }

    system_analysis = {
        "cpu": {
            "avg": 65.4,
            "max": 89.2,
            "min": 12.5,
            "std": 18.7
        },
        "memory": {
            "avg": 72.8,
            "max": 85.6,
            "min": 45.2,
            "std": 12.3
        },
        "disk": {
            "avg": 15.6,
            "max": 25.8,
            "min": 8.9,
            "std": 4.2
        }
    }

    return test_summary, performance_analysis, system_analysis

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="压测报告生成器")
    parser.add_argument("--test-summary", help="测试摘要JSON文件")
    parser.add_argument("--performance-analysis", help="性能分析JSON文件")
    parser.add_argument("--system-analysis", help="系统分析JSON文件")
    parser.add_argument("--charts-dir", help="图表目录")
    parser.add_argument("--output", default="performance_report.html", help="输出HTML文件")
    parser.add_argument("--sample", action="store_true", help="生成示例报告")

    args = parser.parse_args()

    generator = ReportTemplate()

    if args.sample:
        # 生成示例报告
        test_summary, performance_analysis, system_analysis = generate_sample_data()
        output_file = generator.generate_report(
            test_summary=test_summary,
            performance_analysis=performance_analysis,
            system_analysis=system_analysis,
            charts_dir=args.charts_dir,
            output_file=args.output
        )
        print(f"示例报告已生成: {output_file}")
        return

    # 加载实际数据
    test_summary = {}
    performance_analysis = {}
    system_analysis = {}

    if args.test_summary:
        with open(args.test_summary, 'r', encoding='utf-8') as f:
            test_summary = json.load(f)

    if args.performance_analysis:
        with open(args.performance_analysis, 'r', encoding='utf-8') as f:
            performance_analysis = json.load(f)

    if args.system_analysis:
        with open(args.system_analysis, 'r', encoding='utf-8') as f:
            system_analysis = json.load(f)

    if not test_summary and not performance_analysis:
        print("错误: 至少需要提供测试摘要或性能分析数据")
        print("使用 --sample 参数生成示例报告")
        return 1

    # 生成报告
    output_file = generator.generate_report(
        test_summary=test_summary,
        performance_analysis=performance_analysis,
        system_analysis=system_analysis,
        charts_dir=args.charts_dir,
        output_file=args.output
    )

    print(f"报告已生成: {output_file}")
    return 0

if __name__ == "__main__":
    import sys
    sys.exit(main())
