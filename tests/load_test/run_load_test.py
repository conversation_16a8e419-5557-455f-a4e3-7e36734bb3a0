#!/usr/bin/env python3
"""
压测启动脚本
支持多种测试场景和环境配置
"""

import os
import sys
import argparse
import subprocess
import time
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from tests.load_test.load_test_config import (
    get_config, list_scenarios, list_environments,
    LOAD_TEST_SCENARIOS, ENVIRONMENT_CONFIGS
)

def ensure_results_directory():
    """确保结果目录存在"""
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    return results_dir

def run_load_test(scenario: str, environment: str = "local", custom_args: List[str] = None) -> int:
    """运行压测"""
    try:
        # 获取配置
        config = get_config(scenario, environment)
        
        # 确保结果目录存在
        results_dir = ensure_results_directory()
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 更新输出文件路径
        if config.csv_prefix:
            config.csv_prefix = f"results/{scenario}_{environment}_{timestamp}"
        if config.html_report:
            config.html_report = f"results/{scenario}_{environment}_{timestamp}_report.html"
        
        # 构建命令
        cmd = config.to_command_args()
        
        # 添加自定义参数
        if custom_args:
            cmd.extend(custom_args)
        
        print(f"=== 开始压测 ===")
        print(f"场景: {config.name}")
        print(f"描述: {config.description}")
        print(f"环境: {environment}")
        print(f"目标: {config.host}")
        print(f"并发用户: {config.users}")
        print(f"生成速率: {config.spawn_rate} 用户/秒")
        print(f"运行时间: {config.run_time}")
        print(f"命令: {' '.join(cmd)}")
        print("=" * 50)
        
        # 切换到测试目录
        test_dir = Path(__file__).parent
        os.chdir(test_dir)
        
        # 运行压测
        start_time = time.time()
        result = subprocess.run(cmd, capture_output=False)
        end_time = time.time()
        
        print(f"\n=== 压测完成 ===")
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        print(f"退出码: {result.returncode}")
        
        if config.html_report and Path(config.html_report).exists():
            print(f"HTML报告: {config.html_report}")
        
        if config.csv_prefix:
            csv_files = list(Path(".").glob(f"{config.csv_prefix}*.csv"))
            if csv_files:
                print(f"CSV文件: {[str(f) for f in csv_files]}")
        
        return result.returncode
        
    except Exception as e:
        print(f"压测执行失败: {e}")
        return 1

def run_health_check(host: str) -> bool:
    """运行健康检查"""
    import requests
    
    try:
        health_url = f"{host}/health"
        response = requests.get(health_url, timeout=10)
        
        if response.status_code == 200:
            print(f"✅ 健康检查通过: {health_url}")
            return True
        else:
            print(f"❌ 健康检查失败: {health_url} (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {health_url} ({e})")
        return False

def generate_test_plan(output_file: str = "test_plan.json"):
    """生成测试计划文件"""
    test_plan = {
        "metadata": {
            "created_at": datetime.now().isoformat(),
            "description": "API压测计划",
            "version": "1.0"
        },
        "scenarios": {},
        "environments": ENVIRONMENT_CONFIGS
    }
    
    for name, config in LOAD_TEST_SCENARIOS.items():
        test_plan["scenarios"][name] = {
            "name": config.name,
            "description": config.description,
            "users": config.users,
            "spawn_rate": config.spawn_rate,
            "run_time": config.run_time,
            "headless": config.headless
        }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(test_plan, f, indent=2, ensure_ascii=False)
    
    print(f"测试计划已生成: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="API压测启动脚本")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 运行压测命令
    run_parser = subparsers.add_parser("run", help="运行压测")
    run_parser.add_argument("scenario", choices=list(LOAD_TEST_SCENARIOS.keys()), 
                           help="测试场景")
    run_parser.add_argument("-e", "--environment", default="local",
                           choices=list(ENVIRONMENT_CONFIGS.keys()),
                           help="目标环境 (默认: local)")
    run_parser.add_argument("--args", nargs="*", help="额外的locust参数")
    run_parser.add_argument("--no-health-check", action="store_true",
                           help="跳过健康检查")
    
    # 列出场景命令
    list_parser = subparsers.add_parser("list", help="列出可用的场景和环境")
    list_parser.add_argument("--scenarios", action="store_true", help="列出测试场景")
    list_parser.add_argument("--environments", action="store_true", help="列出环境")
    
    # 健康检查命令
    health_parser = subparsers.add_parser("health", help="运行健康检查")
    health_parser.add_argument("-e", "--environment", default="local",
                              choices=list(ENVIRONMENT_CONFIGS.keys()),
                              help="目标环境 (默认: local)")
    
    # 生成测试计划命令
    plan_parser = subparsers.add_parser("plan", help="生成测试计划")
    plan_parser.add_argument("-o", "--output", default="test_plan.json",
                            help="输出文件路径 (默认: test_plan.json)")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    if args.command == "run":
        # 健康检查
        if not args.no_health_check:
            config = get_config(args.scenario, args.environment)
            if not run_health_check(config.host):
                print("健康检查失败，建议检查服务状态后再运行压测")
                return 1
        
        # 运行压测
        return run_load_test(args.scenario, args.environment, args.args)
    
    elif args.command == "list":
        if args.scenarios or (not args.scenarios and not args.environments):
            print("可用的测试场景:")
            for name, desc in list_scenarios().items():
                config = LOAD_TEST_SCENARIOS[name]
                print(f"  {name:12} - {desc}")
                print(f"               用户数: {config.users}, 运行时间: {config.run_time}")
        
        if args.environments or (not args.scenarios and not args.environments):
            print("\n可用的环境:")
            for name, desc in list_environments().items():
                host = ENVIRONMENT_CONFIGS[name]["host"]
                print(f"  {name:12} - {desc} ({host})")
    
    elif args.command == "health":
        host = ENVIRONMENT_CONFIGS[args.environment]["host"]
        success = run_health_check(host)
        return 0 if success else 1
    
    elif args.command == "plan":
        generate_test_plan(args.output)
        return 0
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
