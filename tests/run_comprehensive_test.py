#!/usr/bin/env python3
"""
综合压测工具启动脚本
集成压测、监控、分析和报告生成功能
"""

import os
import sys
import subprocess
import asyncio
import time
import json
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from tests.performance.performance_test import APIPerformanceTester, TEST_CONFIGURATIONS
from tests.monitoring.system_monitor import SystemMonitor
from tests.monitoring.performance_analyzer import PerformanceAnalyzer
from tests.reports.report_template import ReportTemplate

class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.output_dir = Path(config.get("output_dir", "test_results"))
        self.output_dir.mkdir(exist_ok=True)
        
        # 生成时间戳
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.test_name = f"{config.get('test_name', 'comprehensive_test')}_{self.timestamp}"
        
        # 初始化组件
        self.system_monitor = SystemMonitor(interval=config.get("monitor_interval", 1.0))
        self.performance_tester = None
        self.analyzer = PerformanceAnalyzer()
        self.report_generator = ReportTemplate()
        
        # 结果文件路径
        self.results = {
            "performance_data": None,
            "system_data": None,
            "test_summary": None,
            "analysis_report": None,
            "html_report": None,
            "charts_dir": None
        }
    
    async def run_performance_test(self) -> Dict[str, Any]:
        """运行性能测试"""
        print("=== 开始性能测试 ===")
        
        api_url = self.config.get("api_url", "http://localhost:8080/api/v1")
        api_token = self.config.get("api_token", "Bear ipd-OOabxNh6usxsPgTt6EYZHqE1")
        test_config = self.config.get("test_config", TEST_CONFIGURATIONS["standard"])
        
        async with APIPerformanceTester(api_url, api_token) as tester:
            self.performance_tester = tester
            
            # 开始系统监控
            self.system_monitor.start_monitoring()
            
            try:
                # 运行性能测试
                summary = await tester.run_comprehensive_test(test_config)
                
                # 保存性能测试结果
                perf_files = tester.monitor.save_results(str(self.output_dir), self.test_name)
                self.results["performance_data"] = perf_files["raw_data"]
                self.results["test_summary"] = perf_files["summary"]
                
                return summary
                
            finally:
                # 停止系统监控
                self.system_monitor.stop_monitoring()
                
                # 保存系统监控结果
                sys_files = self.system_monitor.save_data(str(self.output_dir), f"{self.test_name}_system")
                self.results["system_data"] = sys_files["raw_data"]
    
    def run_locust_test(self) -> Optional[Dict[str, Any]]:
        """运行Locust压测"""
        print("=== 开始Locust压测 ===")
        
        locust_config = self.config.get("locust_config")
        if not locust_config:
            print("跳过Locust测试（未配置）")
            return None
        
        # 构建Locust命令
        cmd = [
            "locust",
            "-f", "tests/load_test/locustfile.py",
            "--host", self.config.get("api_url", "http://localhost:8080/api/v1"),
            "--users", str(locust_config.get("users", 50)),
            "--spawn-rate", str(locust_config.get("spawn_rate", 5)),
            "--run-time", locust_config.get("run_time", "10m"),
            "--headless",
            "--csv", str(self.output_dir / f"{self.test_name}_locust"),
            "--html", str(self.output_dir / f"{self.test_name}_locust_report.html")
        ]
        
        # 设置环境变量
        env = os.environ.copy()
        env["API_ACCESS_TOKEN"] = self.config.get("api_token", "Bear ipd-OOabxNh6usxsPgTt6EYZHqE1")
        
        try:
            # 开始系统监控
            self.system_monitor.start_monitoring()
            
            # 运行Locust
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, env=env, capture_output=True, text=True, cwd=Path(__file__).parent.parent)
            
            if result.returncode == 0:
                print("Locust测试完成")
                return {"status": "success", "output": result.stdout}
            else:
                print(f"Locust测试失败: {result.stderr}")
                return {"status": "failed", "error": result.stderr}
                
        except Exception as e:
            print(f"Locust测试异常: {e}")
            return {"status": "error", "error": str(e)}
            
        finally:
            # 停止系统监控
            self.system_monitor.stop_monitoring()
            
            # 保存系统监控结果
            sys_files = self.system_monitor.save_data(str(self.output_dir), f"{self.test_name}_system")
            self.results["system_data"] = sys_files["raw_data"]
    
    def analyze_results(self):
        """分析测试结果"""
        print("=== 开始结果分析 ===")
        
        try:
            # 加载数据
            if self.results["performance_data"]:
                self.analyzer.load_performance_data(self.results["performance_data"])
            
            if self.results["system_data"]:
                self.analyzer.load_system_data(self.results["system_data"])
            
            if self.results["test_summary"]:
                self.analyzer.load_test_summary(self.results["test_summary"])
            
            # 生成图表
            charts_dir = self.output_dir / f"{self.test_name}_charts"
            self.analyzer.generate_charts(str(charts_dir))
            self.results["charts_dir"] = str(charts_dir)
            
            # 生成分析报告
            analysis_file = self.output_dir / f"{self.test_name}_analysis.json"
            report = self.analyzer.generate_report(str(analysis_file))
            self.results["analysis_report"] = str(analysis_file)
            
            print("结果分析完成")
            return report
            
        except Exception as e:
            print(f"结果分析失败: {e}")
            return None
    
    def generate_html_report(self):
        """生成HTML报告"""
        print("=== 生成HTML报告 ===")
        
        try:
            # 加载数据
            test_summary = {}
            performance_analysis = {}
            system_analysis = {}
            
            if self.results["test_summary"]:
                with open(self.results["test_summary"], 'r', encoding='utf-8') as f:
                    test_summary = json.load(f)
            
            if self.results["analysis_report"]:
                with open(self.results["analysis_report"], 'r', encoding='utf-8') as f:
                    analysis_data = json.load(f)
                    performance_analysis = analysis_data.get("response_time_analysis", {})
                    system_analysis = analysis_data.get("system_resource_analysis", {})
            
            # 生成HTML报告
            html_file = self.output_dir / f"{self.test_name}_report.html"
            self.report_generator.generate_report(
                test_summary=test_summary,
                performance_analysis=performance_analysis,
                system_analysis=system_analysis,
                charts_dir=self.results["charts_dir"],
                output_file=str(html_file)
            )
            
            self.results["html_report"] = str(html_file)
            print(f"HTML报告已生成: {html_file}")
            return str(html_file)
            
        except Exception as e:
            print(f"HTML报告生成失败: {e}")
            return None
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print(f"=== 开始综合测试: {self.test_name} ===")
        start_time = time.time()
        
        try:
            # 1. 运行性能测试
            if self.config.get("enable_performance_test", True):
                await self.run_performance_test()
            
            # 2. 运行Locust压测
            if self.config.get("enable_locust_test", False):
                self.run_locust_test()
            
            # 3. 分析结果
            if self.config.get("enable_analysis", True):
                self.analyze_results()
            
            # 4. 生成报告
            if self.config.get("enable_html_report", True):
                self.generate_html_report()
            
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"=== 综合测试完成 ===")
            print(f"总耗时: {duration:.1f} 秒")
            print(f"结果目录: {self.output_dir}")
            
            # 输出结果文件
            print("\n生成的文件:")
            for key, file_path in self.results.items():
                if file_path:
                    print(f"  {key}: {file_path}")
            
            return self.results
            
        except Exception as e:
            print(f"综合测试失败: {e}")
            raise

def load_config(config_file: str) -> Dict[str, Any]:
    """加载配置文件"""
    if not Path(config_file).exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def create_default_config() -> Dict[str, Any]:
    """创建默认配置"""
    return {
        "test_name": "comprehensive_test",
        "api_url": "http://localhost:8080/api/v1",
        "api_token": "Bear ipd-OOabxNh6usxsPgTt6EYZHqE1",
        "output_dir": "test_results",
        "monitor_interval": 1.0,
        "enable_performance_test": True,
        "enable_locust_test": False,
        "enable_analysis": True,
        "enable_html_report": True,
        "test_config": {
            "name": "标准测试",
            "endpoints": ["/llm-qa", "/rag-qa", "/data-qa"],
            "duration_seconds": 300,
            "concurrent_requests": 20
        },
        "locust_config": {
            "users": 50,
            "spawn_rate": 5,
            "run_time": "10m"
        }
    }

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="综合压测工具")
    parser.add_argument("--config", help="配置文件路径")
    parser.add_argument("--output-dir", default="test_results", help="输出目录")
    parser.add_argument("--test-name", help="测试名称")
    parser.add_argument("--api-url", default="http://localhost:8080/api/v1", help="API地址")
    parser.add_argument("--duration", type=int, default=300, help="测试持续时间（秒）")
    parser.add_argument("--concurrency", type=int, default=20, help="并发数")
    parser.add_argument("--create-config", help="创建默认配置文件")
    parser.add_argument("--locust", action="store_true", help="启用Locust测试")
    
    args = parser.parse_args()
    
    # 创建默认配置文件
    if args.create_config:
        config = create_default_config()
        with open(args.create_config, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        print(f"默认配置文件已创建: {args.create_config}")
        return 0
    
    # 加载配置
    if args.config:
        config = load_config(args.config)
    else:
        config = create_default_config()
    
    # 命令行参数覆盖配置
    if args.output_dir:
        config["output_dir"] = args.output_dir
    if args.test_name:
        config["test_name"] = args.test_name
    if args.api_url:
        config["api_url"] = args.api_url
    if args.duration:
        config["test_config"]["duration_seconds"] = args.duration
    if args.concurrency:
        config["test_config"]["concurrent_requests"] = args.concurrency
    if args.locust:
        config["enable_locust_test"] = True
    
    # 运行综合测试
    runner = ComprehensiveTestRunner(config)
    try:
        await runner.run_comprehensive_test()
        return 0
    except Exception as e:
        print(f"测试失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
