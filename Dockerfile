FROM python:3.9.21

# 设置公司内部 pip 源
RUN python -m pip config set global.index-url https://pkgs.d.xiaomi.net/artifactory/api/pypi/pypi-virtual/simple
# 设置公司内部 Debian 源
RUN echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm main contrib non-free non-free-firmware' > /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm-updates main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian bookworm-backports main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    echo 'deb https://pkgs.d.xiaomi.net/artifactory/debian-remote/debian-security bookworm-security main contrib non-free non-free-firmware' >> /etc/apt/sources.list && \
    rm -f /etc/apt/sources.list.d/*.list

# 升级pip并安装 wheel
RUN python -m pip install --upgrade pip wheel --no-cache-dir

# 安装基础依赖
COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir --use-pep517 -r /tmp/requirements.txt uvicorn

# 安装 OpenTelemetry 依赖（带版本兼容逻辑）
COPY requirements.hera.txt /tmp/requirements.hera.txt
RUN pip install --no-cache-dir --use-pep517 -r /tmp/requirements.hera.txt

# 设置工作环境
# WORKDIR /home/<USER>
# COPY . ./bin

EXPOSE 8080
