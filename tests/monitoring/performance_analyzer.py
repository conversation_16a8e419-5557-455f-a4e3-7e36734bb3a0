#!/usr/bin/env python3
"""
性能分析工具
分析压测结果和系统监控数据，生成性能报告
"""

import json
import statistics
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import argparse
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self):
        self.performance_data = []
        self.system_data = []
        self.test_summary = {}
    
    def load_performance_data(self, file_path: str):
        """加载性能测试数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            self.performance_data = json.load(f)
        print(f"已加载性能数据: {len(self.performance_data)} 条记录")
    
    def load_system_data(self, file_path: str):
        """加载系统监控数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            self.system_data = json.load(f)
        print(f"已加载系统数据: {len(self.system_data)} 条记录")
    
    def load_test_summary(self, file_path: str):
        """加载测试摘要数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            self.test_summary = json.load(f)
        print(f"已加载测试摘要")
    
    def analyze_response_times(self) -> Dict[str, Any]:
        """分析响应时间"""
        if not self.performance_data:
            return {}
        
        # 按端点分组分析
        endpoint_analysis = {}
        endpoints = set(item['endpoint'] for item in self.performance_data)
        
        for endpoint in endpoints:
            endpoint_data = [item for item in self.performance_data if item['endpoint'] == endpoint]
            successful_data = [item for item in endpoint_data if item['success']]
            
            if successful_data:
                response_times = [item['response_time'] for item in successful_data]
                
                endpoint_analysis[endpoint] = {
                    "total_requests": len(endpoint_data),
                    "successful_requests": len(successful_data),
                    "error_rate": (len(endpoint_data) - len(successful_data)) / len(endpoint_data),
                    "avg_response_time": statistics.mean(response_times),
                    "median_response_time": statistics.median(response_times),
                    "min_response_time": min(response_times),
                    "max_response_time": max(response_times),
                    "p95_response_time": statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else max(response_times),
                    "p99_response_time": statistics.quantiles(response_times, n=100)[98] if len(response_times) > 100 else max(response_times),
                    "std_response_time": statistics.stdev(response_times) if len(response_times) > 1 else 0
                }
        
        return endpoint_analysis
    
    def analyze_throughput(self) -> Dict[str, Any]:
        """分析吞吐量"""
        if not self.performance_data:
            return {}
        
        # 按时间窗口计算吞吐量
        df = pd.DataFrame(self.performance_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df.set_index('timestamp', inplace=True)
        
        # 按分钟统计
        throughput_per_minute = df.groupby([df.index.floor('1min'), 'endpoint']).size().unstack(fill_value=0)
        
        # 按端点统计
        endpoint_throughput = {}
        for endpoint in throughput_per_minute.columns:
            values = throughput_per_minute[endpoint].values
            endpoint_throughput[endpoint] = {
                "avg_rps": values.mean() / 60,  # 转换为每秒
                "max_rps": values.max() / 60,
                "min_rps": values.min() / 60,
                "total_requests": values.sum()
            }
        
        return {
            "by_endpoint": endpoint_throughput,
            "timeline": throughput_per_minute.to_dict()
        }
    
    def analyze_errors(self) -> Dict[str, Any]:
        """分析错误情况"""
        if not self.performance_data:
            return {}
        
        error_data = [item for item in self.performance_data if not item['success']]
        
        # 按端点统计错误
        endpoint_errors = {}
        endpoints = set(item['endpoint'] for item in self.performance_data)
        
        for endpoint in endpoints:
            endpoint_total = len([item for item in self.performance_data if item['endpoint'] == endpoint])
            endpoint_errors_count = len([item for item in error_data if item['endpoint'] == endpoint])
            
            endpoint_errors[endpoint] = {
                "total_requests": endpoint_total,
                "error_count": endpoint_errors_count,
                "error_rate": endpoint_errors_count / endpoint_total if endpoint_total > 0 else 0
            }
        
        # 按错误类型统计
        error_types = {}
        for item in error_data:
            error_msg = item.get('error_message', 'Unknown')
            if error_msg not in error_types:
                error_types[error_msg] = 0
            error_types[error_msg] += 1
        
        return {
            "by_endpoint": endpoint_errors,
            "by_type": error_types,
            "total_errors": len(error_data),
            "total_requests": len(self.performance_data),
            "overall_error_rate": len(error_data) / len(self.performance_data) if self.performance_data else 0
        }
    
    def analyze_system_resources(self) -> Dict[str, Any]:
        """分析系统资源使用"""
        if not self.system_data:
            return {}
        
        cpu_values = [item['cpu_percent'] for item in self.system_data]
        memory_values = [item['memory_percent'] for item in self.system_data]
        disk_values = [item['disk_percent'] for item in self.system_data]
        
        return {
            "cpu": {
                "avg": statistics.mean(cpu_values),
                "max": max(cpu_values),
                "min": min(cpu_values),
                "std": statistics.stdev(cpu_values) if len(cpu_values) > 1 else 0
            },
            "memory": {
                "avg": statistics.mean(memory_values),
                "max": max(memory_values),
                "min": min(memory_values),
                "std": statistics.stdev(memory_values) if len(memory_values) > 1 else 0
            },
            "disk": {
                "avg": statistics.mean(disk_values),
                "max": max(disk_values),
                "min": min(disk_values),
                "std": statistics.stdev(disk_values) if len(disk_values) > 1 else 0
            }
        }
    
    def generate_charts(self, output_dir: str):
        """生成图表"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        
        # 1. 响应时间分布图
        if self.performance_data:
            self._plot_response_time_distribution(output_path)
            self._plot_response_time_timeline(output_path)
            self._plot_throughput_timeline(output_path)
            self._plot_error_rate_by_endpoint(output_path)
        
        # 2. 系统资源使用图
        if self.system_data:
            self._plot_system_resources(output_path)
        
        print(f"图表已生成到: {output_path}")
    
    def _plot_response_time_distribution(self, output_path: Path):
        """绘制响应时间分布图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('响应时间分析', fontsize=16)
        
        # 按端点分组
        endpoints = set(item['endpoint'] for item in self.performance_data)
        
        # 响应时间箱线图
        endpoint_response_times = {}
        for endpoint in endpoints:
            endpoint_data = [item for item in self.performance_data 
                           if item['endpoint'] == endpoint and item['success']]
            if endpoint_data:
                endpoint_response_times[endpoint] = [item['response_time'] for item in endpoint_data]
        
        if endpoint_response_times:
            axes[0, 0].boxplot(endpoint_response_times.values(), labels=endpoint_response_times.keys())
            axes[0, 0].set_title('响应时间箱线图')
            axes[0, 0].set_ylabel('响应时间 (ms)')
            axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 响应时间直方图
        all_response_times = [item['response_time'] for item in self.performance_data if item['success']]
        if all_response_times:
            axes[0, 1].hist(all_response_times, bins=50, alpha=0.7)
            axes[0, 1].set_title('响应时间分布')
            axes[0, 1].set_xlabel('响应时间 (ms)')
            axes[0, 1].set_ylabel('频次')
        
        # 成功率饼图
        success_count = len([item for item in self.performance_data if item['success']])
        error_count = len(self.performance_data) - success_count
        if success_count + error_count > 0:
            axes[1, 0].pie([success_count, error_count], 
                          labels=['成功', '失败'], 
                          autopct='%1.1f%%',
                          colors=['green', 'red'])
            axes[1, 0].set_title('请求成功率')
        
        # 端点请求量对比
        endpoint_counts = {}
        for endpoint in endpoints:
            endpoint_counts[endpoint] = len([item for item in self.performance_data 
                                           if item['endpoint'] == endpoint])
        
        if endpoint_counts:
            axes[1, 1].bar(endpoint_counts.keys(), endpoint_counts.values())
            axes[1, 1].set_title('各端点请求量')
            axes[1, 1].set_ylabel('请求数')
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path / 'response_time_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_response_time_timeline(self, output_path: Path):
        """绘制响应时间时间线图"""
        df = pd.DataFrame(self.performance_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 按端点分组绘制
        fig, ax = plt.subplots(figsize=(15, 8))
        
        endpoints = df['endpoint'].unique()
        for endpoint in endpoints:
            endpoint_data = df[df['endpoint'] == endpoint]
            successful_data = endpoint_data[endpoint_data['success'] == True]
            
            if not successful_data.empty:
                # 按时间窗口聚合
                time_grouped = successful_data.set_index('timestamp').resample('1min')['response_time'].mean()
                ax.plot(time_grouped.index, time_grouped.values, label=endpoint, marker='o', markersize=3)
        
        ax.set_title('响应时间时间线')
        ax.set_xlabel('时间')
        ax.set_ylabel('平均响应时间 (ms)')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'response_time_timeline.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_throughput_timeline(self, output_path: Path):
        """绘制吞吐量时间线图"""
        df = pd.DataFrame(self.performance_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        fig, ax = plt.subplots(figsize=(15, 8))

        # 按分钟统计吞吐量
        throughput_per_minute = df.groupby([df['timestamp'].dt.floor('1min'), 'endpoint']).size().unstack(fill_value=0)

        for endpoint in throughput_per_minute.columns:
            # 转换为每秒请求数
            rps_data = throughput_per_minute[endpoint] / 60
            ax.plot(throughput_per_minute.index, rps_data.values, label=endpoint, marker='o', markersize=3)

        ax.set_title('吞吐量时间线')
        ax.set_xlabel('时间')
        ax.set_ylabel('请求数/秒 (RPS)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'throughput_timeline.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_error_rate_by_endpoint(self, output_path: Path):
        """绘制各端点错误率图"""
        error_analysis = self.analyze_errors()

        if not error_analysis.get('by_endpoint'):
            return

        endpoints = list(error_analysis['by_endpoint'].keys())
        error_rates = [error_analysis['by_endpoint'][ep]['error_rate'] * 100 for ep in endpoints]

        fig, ax = plt.subplots(figsize=(12, 6))
        bars = ax.bar(endpoints, error_rates, color=['red' if rate > 5 else 'orange' if rate > 1 else 'green' for rate in error_rates])

        ax.set_title('各端点错误率')
        ax.set_xlabel('端点')
        ax.set_ylabel('错误率 (%)')
        ax.set_ylim(0, max(error_rates) * 1.1 if error_rates else 1)

        # 添加数值标签
        for bar, rate in zip(bars, error_rates):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                   f'{rate:.1f}%', ha='center', va='bottom')

        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig(output_path / 'error_rate_by_endpoint.png', dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_system_resources(self, output_path: Path):
        """绘制系统资源使用图"""
        df = pd.DataFrame(self.system_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('系统资源使用情况', fontsize=16)

        # CPU使用率
        axes[0, 0].plot(df['timestamp'], df['cpu_percent'], color='blue', linewidth=1)
        axes[0, 0].set_title('CPU使用率')
        axes[0, 0].set_ylabel('CPU (%)')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 内存使用率
        axes[0, 1].plot(df['timestamp'], df['memory_percent'], color='green', linewidth=1)
        axes[0, 1].set_title('内存使用率')
        axes[0, 1].set_ylabel('内存 (%)')
        axes[0, 1].grid(True, alpha=0.3)
        axes[0, 1].tick_params(axis='x', rotation=45)

        # 磁盘IO
        axes[1, 0].plot(df['timestamp'], df['disk_read_mb_per_sec'], label='读取', color='orange')
        axes[1, 0].plot(df['timestamp'], df['disk_write_mb_per_sec'], label='写入', color='red')
        axes[1, 0].set_title('磁盘IO')
        axes[1, 0].set_ylabel('MB/s')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 网络IO
        axes[1, 1].plot(df['timestamp'], df['network_sent_mb_per_sec'], label='发送', color='purple')
        axes[1, 1].plot(df['timestamp'], df['network_recv_mb_per_sec'], label='接收', color='brown')
        axes[1, 1].set_title('网络IO')
        axes[1, 1].set_ylabel('MB/s')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(output_path / 'system_resources.png', dpi=300, bbox_inches='tight')
        plt.close()

    def generate_report(self, output_file: str):
        """生成性能分析报告"""
        report = {
            "generated_at": datetime.now().isoformat(),
            "test_summary": self.test_summary,
            "response_time_analysis": self.analyze_response_times(),
            "throughput_analysis": self.analyze_throughput(),
            "error_analysis": self.analyze_errors(),
            "system_resource_analysis": self.analyze_system_resources()
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        print(f"性能分析报告已生成: {output_file}")
        return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="性能分析工具")
    parser.add_argument("--performance-data", required=True, help="性能测试数据文件")
    parser.add_argument("--system-data", help="系统监控数据文件")
    parser.add_argument("--test-summary", help="测试摘要文件")
    parser.add_argument("--output-dir", default="analysis_results", help="输出目录")
    parser.add_argument("--charts", action="store_true", help="生成图表")
    parser.add_argument("--report", action="store_true", help="生成分析报告")

    args = parser.parse_args()

    # 创建分析器
    analyzer = PerformanceAnalyzer()

    # 加载数据
    try:
        analyzer.load_performance_data(args.performance_data)
    except Exception as e:
        print(f"加载性能数据失败: {e}")
        return 1

    if args.system_data:
        try:
            analyzer.load_system_data(args.system_data)
        except Exception as e:
            print(f"加载系统数据失败: {e}")

    if args.test_summary:
        try:
            analyzer.load_test_summary(args.test_summary)
        except Exception as e:
            print(f"加载测试摘要失败: {e}")

    # 创建输出目录
    output_path = Path(args.output_dir)
    output_path.mkdir(exist_ok=True)

    # 生成分析
    print("\n=== 响应时间分析 ===")
    response_analysis = analyzer.analyze_response_times()
    for endpoint, stats in response_analysis.items():
        print(f"{endpoint}:")
        print(f"  总请求数: {stats['total_requests']}")
        print(f"  成功率: {(1-stats['error_rate'])*100:.1f}%")
        print(f"  平均响应时间: {stats['avg_response_time']:.2f}ms")
        print(f"  P95响应时间: {stats['p95_response_time']:.2f}ms")
        print(f"  P99响应时间: {stats['p99_response_time']:.2f}ms")

    print("\n=== 错误分析 ===")
    error_analysis = analyzer.analyze_errors()
    print(f"总错误率: {error_analysis['overall_error_rate']*100:.2f}%")
    print(f"总错误数: {error_analysis['total_errors']}")

    if analyzer.system_data:
        print("\n=== 系统资源分析 ===")
        system_analysis = analyzer.analyze_system_resources()
        print(f"平均CPU使用率: {system_analysis['cpu']['avg']:.1f}%")
        print(f"最大CPU使用率: {system_analysis['cpu']['max']:.1f}%")
        print(f"平均内存使用率: {system_analysis['memory']['avg']:.1f}%")
        print(f"最大内存使用率: {system_analysis['memory']['max']:.1f}%")

    # 生成图表
    if args.charts:
        try:
            analyzer.generate_charts(args.output_dir)
        except Exception as e:
            print(f"生成图表失败: {e}")

    # 生成报告
    if args.report:
        report_file = output_path / "performance_analysis_report.json"
        try:
            analyzer.generate_report(str(report_file))
        except Exception as e:
            print(f"生成报告失败: {e}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
