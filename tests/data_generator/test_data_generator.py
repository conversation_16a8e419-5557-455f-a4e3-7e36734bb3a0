#!/usr/bin/env python3
"""
测试数据生成器
用于生成多样化的测试请求数据
"""

import json
import uuid
import random
import itertools
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Generator
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self, data_file: Optional[str] = None):
        """
        初始化测试数据生成器
        
        Args:
            data_file: 测试数据文件路径，如果不提供则使用默认数据
        """
        self.queries = self._load_queries(data_file)
        self.model_ids = ["qwen3_32b", "qwen3_235b_2507", "gpt_4o"]
        self.user_id_prefixes = ["test_user", "load_test", "perf_test", "stress_test"]
        self.conversation_patterns = ["single", "multi_turn", "long_context"]
    
    def _load_queries(self, data_file: Optional[str]) -> Dict[str, List[str]]:
        """加载查询数据"""
        if data_file and Path(data_file).exists():
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    # 如果是简单的查询列表
                    return {"general": [item.get("query", item) if isinstance(item, dict) else item for item in data]}
                elif isinstance(data, dict):
                    return data
        
        # 默认测试数据
        return {
            "hardware": [
                "器件距离板边最小距离",
                "PDN仿真不通过如何优化",
                "分板筋距离RF Connectors要求",
                "无工艺边拼板的要求有哪些",
                "板振问题案例",
                "FPC点击微动失效怎么解决",
                "双层FPC的归一化叠层有哪些",
                "ACLR参数定义及设计要点",
                "N16U项目EMC问题总结",
                "N16U射频相关问题",
                "PCB设计基础知识",
                "不贴的芯片应该采用什么命名方式",
                "HDI板相关知识",
                "RF连接器的设计要求",
                "EMI/EMC设计规范",
                "高速信号完整性分析",
                "电源完整性设计",
                "热设计和散热方案",
                "机械结构设计要求",
                "可靠性测试标准"
            ],
            "data": [
                "库存周转率如何计算？",
                "什么是供应链管理？",
                "如何优化生产流程？",
                "质量控制的关键指标有哪些？",
                "成本控制的最佳实践",
                "项目管理的关键要素",
                "风险评估的方法",
                "数据分析的基本步骤",
                "KPI指标体系设计",
                "流程优化方法论",
                "精益生产原理",
                "六西格玛质量管理",
                "敏捷开发方法",
                "DevOps实践指南",
                "数字化转型策略",
                "业务流程重组",
                "组织架构优化",
                "人力资源管理",
                "财务分析方法",
                "市场分析技巧"
            ],
            "general": [
                "人工智能的发展趋势",
                "机器学习的应用场景",
                "深度学习的基本原理",
                "自然语言处理技术",
                "计算机视觉应用",
                "大数据处理技术",
                "云计算架构设计",
                "微服务架构模式",
                "容器化技术应用",
                "DevOps工具链",
                "API设计最佳实践",
                "数据库优化技巧",
                "缓存策略设计",
                "负载均衡方案",
                "安全防护措施",
                "性能监控方法",
                "故障排查技巧",
                "代码质量管理",
                "测试自动化",
                "持续集成部署"
            ]
        }
    
    def generate_user_id(self, pattern: str = "random") -> str:
        """生成用户ID"""
        if pattern == "random":
            prefix = random.choice(self.user_id_prefixes)
            suffix = uuid.uuid4().hex[:8]
            return f"{prefix}_{suffix}"
        elif pattern == "sequential":
            timestamp = int(datetime.now().timestamp())
            return f"user_{timestamp}"
        else:
            return pattern
    
    def generate_conversation_id(self) -> str:
        """生成对话ID"""
        return str(uuid.uuid4())
    
    def generate_msg_id(self) -> str:
        """生成消息ID"""
        return str(uuid.uuid4())
    
    def generate_query(self, category: Optional[str] = None, complexity: str = "medium") -> str:
        """
        生成查询文本
        
        Args:
            category: 查询类别 (hardware, data, general)
            complexity: 复杂度 (simple, medium, complex)
        """
        if category and category in self.queries:
            base_queries = self.queries[category]
        else:
            # 随机选择类别
            all_queries = []
            for queries in self.queries.values():
                all_queries.extend(queries)
            base_queries = all_queries
        
        base_query = random.choice(base_queries)
        
        if complexity == "simple":
            return base_query
        elif complexity == "medium":
            # 可能添加一些修饰词
            modifiers = ["详细说明", "具体分析", "举例说明", "深入解释"]
            if random.random() < 0.3:
                return f"{random.choice(modifiers)}{base_query}"
            return base_query
        elif complexity == "complex":
            # 组合多个查询或添加复杂条件
            if random.random() < 0.4 and len(base_queries) > 1:
                second_query = random.choice(base_queries)
                connectors = ["，同时", "，另外", "，以及"]
                return f"{base_query}{random.choice(connectors)}{second_query}"
            else:
                complex_modifiers = [
                    "请详细分析并提供具体案例",
                    "从技术和商业角度分析",
                    "结合实际项目经验说明",
                    "对比不同方案的优缺点"
                ]
                return f"{base_query}，{random.choice(complex_modifiers)}"
        
        return base_query
    
    def generate_history(self, length: int = None, category: Optional[str] = None) -> List[Dict[str, str]]:
        """
        生成对话历史
        
        Args:
            length: 历史长度，如果为None则随机生成
            category: 查询类别
        """
        if length is None:
            length = random.randint(0, 8)
        
        history = []
        for _ in range(length):
            query = self.generate_query(category, "simple")
            # 生成简化的回答
            content = f"关于{query}的回答内容..."
            history.append({"query": query, "content": content})
        
        return history
    
    def generate_llm_qa_request(self, **kwargs) -> Dict[str, Any]:
        """生成LLM问答请求"""
        defaults = {
            "query": self.generate_query(),
            "user_id": self.generate_user_id(),
            "model_id": random.choice(self.model_ids),
            "msg_id": self.generate_msg_id(),
            "conversation_id": self.generate_conversation_id(),
            "history": self.generate_history(),
            "stream": random.choice([True, False]),
            "enable_thinking": random.choice([True, False])
        }
        defaults.update(kwargs)
        return defaults
    
    def generate_rag_qa_request(self, **kwargs) -> Dict[str, Any]:
        """生成RAG问答请求"""
        defaults = {
            "query": self.generate_query("hardware"),
            "user_id": self.generate_user_id(),
            "model_id": random.choice(self.model_ids),
            "msg_id": self.generate_msg_id(),
            "conversation_id": self.generate_conversation_id(),
            "history": self.generate_history(category="hardware"),
            "stream": random.choice([True, False]),
            "top_k": random.randint(5, 30),
            "top_r": random.randint(3, 15),
            "min_score": round(random.uniform(0.1, 0.8), 2),
            "enable_thinking": random.choice([True, False]),
            "mode": random.choice(["strict", "common"]),
            "temperature": round(random.uniform(0.1, 1.0), 2),
            "top_p": round(random.uniform(0.1, 1.0), 2),
            "knowledge": random.choice([None, "car", "hardware"])
        }
        defaults.update(kwargs)
        return defaults
    
    def generate_data_qa_request(self, **kwargs) -> Dict[str, Any]:
        """生成数据问答请求"""
        defaults = {
            "query": self.generate_query("data"),
            "user_id": self.generate_user_id(),
            "model_id": random.choice(self.model_ids),
            "msg_id": self.generate_msg_id(),
            "conversation_id": self.generate_conversation_id(),
            "history": self.generate_history(category="data"),
            "stream": random.choice([True, False]),
            "top_k": random.randint(5, 30),
            "top_r": random.randint(3, 15),
            "min_score": round(random.uniform(0.1, 0.8), 2),
            "enable_thinking": random.choice([True, False]),
            "mode": random.choice(["strict", "common"]),
            "temperature": round(random.uniform(0.1, 1.0), 2),
            "top_p": round(random.uniform(0.1, 1.0), 2)
        }
        defaults.update(kwargs)
        return defaults
    
    def generate_all_qa_request(self, **kwargs) -> Dict[str, Any]:
        """生成全库问答请求"""
        defaults = {
            "query": self.generate_query(),
            "user_id": self.generate_user_id(),
            "model_id": random.choice(self.model_ids),
            "msg_id": self.generate_msg_id(),
            "conversation_id": self.generate_conversation_id(),
            "history": self.generate_history(),
            "stream": random.choice([True, False]),
            "top_k": random.randint(5, 30),
            "top_r": random.randint(3, 15),
            "min_score": round(random.uniform(0.1, 0.8), 2),
            "enable_thinking": random.choice([True, False]),
            "mode": random.choice(["strict", "common"]),
            "temperature": round(random.uniform(0.1, 1.0), 2),
            "top_p": round(random.uniform(0.1, 1.0), 2),
            "collection": random.choice([None, ["car"], ["hardware"], ["data"], ["car", "hardware"]])
        }
        defaults.update(kwargs)
        return defaults
    
    def generate_search_request(self, **kwargs) -> Dict[str, Any]:
        """生成搜索请求"""
        defaults = {
            "query": self.generate_query(),
            "user_id": self.generate_user_id(),
            "msg_id": self.generate_msg_id(),
            "top_k": random.randint(5, 30),
            "top_r": random.randint(3, 15),
            "min_score": round(random.uniform(0.1, 0.8), 2),
            "collections": random.choice([None, ["car"], ["hardware"], ["data"], ["car", "hardware"]])
        }
        defaults.update(kwargs)
        return defaults

    def generate_batch_requests(self, endpoint: str, count: int, **kwargs) -> List[Dict[str, Any]]:
        """
        批量生成请求

        Args:
            endpoint: API端点名称
            count: 生成数量
            **kwargs: 额外参数
        """
        generators = {
            "llm-qa": self.generate_llm_qa_request,
            "rag-qa": self.generate_rag_qa_request,
            "data-qa": self.generate_data_qa_request,
            "all-qa": self.generate_all_qa_request,
            "search": self.generate_search_request
        }

        if endpoint not in generators:
            raise ValueError(f"不支持的端点: {endpoint}")

        generator = generators[endpoint]
        return [generator(**kwargs) for _ in range(count)]

    def generate_test_scenario(self, scenario_config: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        根据场景配置生成测试数据

        Args:
            scenario_config: 场景配置，包含各端点的请求数量等
        """
        scenario_data = {}

        for endpoint, config in scenario_config.items():
            if isinstance(config, int):
                # 简单配置，只指定数量
                count = config
                requests = self.generate_batch_requests(endpoint, count)
            elif isinstance(config, dict):
                # 详细配置
                count = config.get("count", 10)
                params = {k: v for k, v in config.items() if k != "count"}
                requests = self.generate_batch_requests(endpoint, count, **params)
            else:
                continue

            scenario_data[endpoint] = requests

        return scenario_data

    def export_to_file(self, data: Any, output_file: str, format: str = "json"):
        """
        导出数据到文件

        Args:
            data: 要导出的数据
            output_file: 输出文件路径
            format: 文件格式 (json, csv)
        """
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        if format == "json":
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        elif format == "csv":
            import csv
            if isinstance(data, dict):
                # 如果是字典，展平为列表
                flat_data = []
                for endpoint, requests in data.items():
                    for request in requests:
                        request["endpoint"] = endpoint
                        flat_data.append(request)
                data = flat_data

            if data and isinstance(data[0], dict):
                fieldnames = data[0].keys()
                with open(output_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(data)
        else:
            raise ValueError(f"不支持的格式: {format}")

        print(f"数据已导出到: {output_path}")

# 预定义的测试场景配置
TEST_SCENARIOS = {
    "smoke_test": {
        "llm-qa": 5,
        "rag-qa": 5,
        "search": 3
    },
    "load_test": {
        "llm-qa": {"count": 50, "stream": False},
        "rag-qa": {"count": 100, "stream": False, "mode": "common"},
        "data-qa": {"count": 80, "stream": False},
        "all-qa": {"count": 30, "stream": False},
        "search": {"count": 40}
    },
    "stress_test": {
        "llm-qa": {"count": 200, "stream": False, "enable_thinking": False},
        "rag-qa": {"count": 300, "stream": False, "mode": "common", "enable_thinking": False},
        "data-qa": {"count": 250, "stream": False, "enable_thinking": False},
        "all-qa": {"count": 150, "stream": False, "enable_thinking": False},
        "search": {"count": 100}
    },
    "mixed_workload": {
        "llm-qa": {"count": 30, "stream": True},
        "rag-qa": {"count": 40, "stream": False},
        "data-qa": {"count": 35, "stream": True},
        "search": {"count": 25}
    }
}

def main():
    """主函数 - 命令行工具"""
    import argparse

    parser = argparse.ArgumentParser(description="测试数据生成器")
    parser.add_argument("--scenario", choices=list(TEST_SCENARIOS.keys()),
                       help="预定义测试场景")
    parser.add_argument("--endpoint", choices=["llm-qa", "rag-qa", "data-qa", "all-qa", "search"],
                       help="单个端点")
    parser.add_argument("--count", type=int, default=10, help="生成数量")
    parser.add_argument("--output", default="test_data.json", help="输出文件")
    parser.add_argument("--format", choices=["json", "csv"], default="json", help="输出格式")
    parser.add_argument("--data-file", help="自定义查询数据文件")
    parser.add_argument("--list-scenarios", action="store_true", help="列出可用场景")

    args = parser.parse_args()

    if args.list_scenarios:
        print("可用的测试场景:")
        for name, config in TEST_SCENARIOS.items():
            print(f"  {name}:")
            for endpoint, endpoint_config in config.items():
                if isinstance(endpoint_config, int):
                    print(f"    {endpoint}: {endpoint_config} 个请求")
                else:
                    count = endpoint_config.get("count", "未知")
                    print(f"    {endpoint}: {count} 个请求")
        return

    # 创建生成器
    generator = TestDataGenerator(args.data_file)

    if args.scenario:
        # 生成场景数据
        scenario_config = TEST_SCENARIOS[args.scenario]
        data = generator.generate_test_scenario(scenario_config)
        generator.export_to_file(data, args.output, args.format)

        # 统计信息
        total_requests = sum(len(requests) for requests in data.values())
        print(f"场景 '{args.scenario}' 生成完成:")
        print(f"  总请求数: {total_requests}")
        for endpoint, requests in data.items():
            print(f"  {endpoint}: {len(requests)} 个请求")

    elif args.endpoint:
        # 生成单端点数据
        data = generator.generate_batch_requests(args.endpoint, args.count)
        generator.export_to_file(data, args.output, args.format)
        print(f"端点 '{args.endpoint}' 生成 {len(data)} 个请求")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
