#!/usr/bin/env python3
"""
详细的性能测试脚本
包括响应时间、吞吐量、错误率等指标监控
"""

import asyncio
import aiohttp
import time
import json
import uuid
import statistics
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import argparse
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    timestamp: str
    endpoint: str
    response_time: float  # 响应时间(ms)
    status_code: int
    success: bool
    error_message: Optional[str] = None
    request_size: int = 0  # 请求大小(bytes)
    response_size: int = 0  # 响应大小(bytes)

@dataclass
class SystemMetrics:
    """系统指标数据类"""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float

@dataclass
class TestSummary:
    """测试摘要数据类"""
    test_name: str
    start_time: str
    end_time: str
    duration_seconds: float
    total_requests: int
    successful_requests: int
    failed_requests: int
    error_rate: float
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p50_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    total_data_sent_mb: float
    total_data_received_mb: float

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.performance_metrics: List[PerformanceMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        self.monitoring = False
        self.monitor_thread = None
        self.start_time = None
        self.initial_disk_io = None
        self.initial_network_io = None
    
    def start_monitoring(self, interval: float = 1.0):
        """开始系统监控"""
        self.monitoring = True
        self.start_time = time.time()
        
        # 获取初始IO统计
        self.initial_disk_io = psutil.disk_io_counters()
        self.initial_network_io = psutil.net_io_counters()
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_system, 
            args=(interval,)
        )
        self.monitor_thread.start()
    
    def stop_monitoring(self):
        """停止系统监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
    
    def _monitor_system(self, interval: float):
        """系统监控线程"""
        while self.monitoring:
            try:
                # CPU和内存
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                
                # 磁盘IO
                current_disk_io = psutil.disk_io_counters()
                disk_read_mb = 0
                disk_write_mb = 0
                if self.initial_disk_io and current_disk_io:
                    disk_read_mb = (current_disk_io.read_bytes - self.initial_disk_io.read_bytes) / 1024 / 1024
                    disk_write_mb = (current_disk_io.write_bytes - self.initial_disk_io.write_bytes) / 1024 / 1024
                
                # 网络IO
                current_network_io = psutil.net_io_counters()
                network_sent_mb = 0
                network_recv_mb = 0
                if self.initial_network_io and current_network_io:
                    network_sent_mb = (current_network_io.bytes_sent - self.initial_network_io.bytes_sent) / 1024 / 1024
                    network_recv_mb = (current_network_io.bytes_recv - self.initial_network_io.bytes_recv) / 1024 / 1024
                
                metrics = SystemMetrics(
                    timestamp=datetime.now().isoformat(),
                    cpu_percent=cpu_percent,
                    memory_percent=memory.percent,
                    memory_used_mb=memory.used / 1024 / 1024,
                    disk_io_read_mb=disk_read_mb,
                    disk_io_write_mb=disk_write_mb,
                    network_sent_mb=network_sent_mb,
                    network_recv_mb=network_recv_mb
                )
                
                self.system_metrics.append(metrics)
                
            except Exception as e:
                print(f"系统监控错误: {e}")
            
            time.sleep(interval)
    
    def add_performance_metric(self, metric: PerformanceMetrics):
        """添加性能指标"""
        self.performance_metrics.append(metric)
    
    def generate_summary(self, test_name: str) -> TestSummary:
        """生成测试摘要"""
        if not self.performance_metrics:
            raise ValueError("没有性能数据")
        
        # 计算基本统计
        total_requests = len(self.performance_metrics)
        successful_requests = sum(1 for m in self.performance_metrics if m.success)
        failed_requests = total_requests - successful_requests
        error_rate = failed_requests / total_requests if total_requests > 0 else 0
        
        # 响应时间统计
        response_times = [m.response_time for m in self.performance_metrics if m.success]
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p50_response_time = statistics.median(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) > 20 else max_response_time
            p99_response_time = statistics.quantiles(response_times, n=100)[98] if len(response_times) > 100 else max_response_time
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p50_response_time = p95_response_time = p99_response_time = 0
        
        # 时间统计
        start_time = min(m.timestamp for m in self.performance_metrics)
        end_time = max(m.timestamp for m in self.performance_metrics)
        start_dt = datetime.fromisoformat(start_time)
        end_dt = datetime.fromisoformat(end_time)
        duration_seconds = (end_dt - start_dt).total_seconds()
        requests_per_second = total_requests / duration_seconds if duration_seconds > 0 else 0
        
        # 数据传输统计
        total_data_sent_mb = sum(m.request_size for m in self.performance_metrics) / 1024 / 1024
        total_data_received_mb = sum(m.response_size for m in self.performance_metrics) / 1024 / 1024
        
        return TestSummary(
            test_name=test_name,
            start_time=start_time,
            end_time=end_time,
            duration_seconds=duration_seconds,
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            error_rate=error_rate,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p50_response_time=p50_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            total_data_sent_mb=total_data_sent_mb,
            total_data_received_mb=total_data_received_mb
        )
    
    def save_results(self, output_dir: str, test_name: str):
        """保存测试结果"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存性能指标
        perf_file = output_path / f"{test_name}_{timestamp}_performance.json"
        with open(perf_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(m) for m in self.performance_metrics], f, indent=2, ensure_ascii=False)
        
        # 保存系统指标
        sys_file = output_path / f"{test_name}_{timestamp}_system.json"
        with open(sys_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(m) for m in self.system_metrics], f, indent=2, ensure_ascii=False)
        
        # 保存测试摘要
        summary = self.generate_summary(test_name)
        summary_file = output_path / f"{test_name}_{timestamp}_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(summary), f, indent=2, ensure_ascii=False)
        
        print(f"测试结果已保存到: {output_path}")
        return summary

class APIPerformanceTester:
    """API性能测试器"""
    
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.monitor = PerformanceMonitor()
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=50)
        timeout = aiohttp.ClientTimeout(total=300)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"Authorization": self.auth_token}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def make_request(self, endpoint: str, payload: Dict[str, Any]) -> PerformanceMetrics:
        """发送单个请求并记录性能指标"""
        url = f"{self.base_url}{endpoint}"
        request_data = json.dumps(payload, ensure_ascii=False).encode('utf-8')
        request_size = len(request_data)
        
        start_time = time.time()
        timestamp = datetime.now().isoformat()
        
        try:
            async with self.session.post(url, json=payload) as response:
                response_text = await response.text()
                response_size = len(response_text.encode('utf-8'))
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                success = 200 <= response.status < 300
                
                return PerformanceMetrics(
                    timestamp=timestamp,
                    endpoint=endpoint,
                    response_time=response_time,
                    status_code=response.status,
                    success=success,
                    error_message=None if success else f"HTTP {response.status}: {response_text[:200]}",
                    request_size=request_size,
                    response_size=response_size
                )
        
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            
            return PerformanceMetrics(
                timestamp=timestamp,
                endpoint=endpoint,
                response_time=response_time,
                status_code=0,
                success=False,
                error_message=str(e),
                request_size=request_size,
                response_size=0
            )
    
    def generate_test_payload(self, endpoint: str) -> Dict[str, Any]:
        """生成测试负载"""
        msg_id = str(uuid.uuid4())
        conversation_id = str(uuid.uuid4())
        
        base_payload = {
            "query": "PCB设计基础知识",
            "user_id": "perf_test_user",
            "model_id": "qwen3_32b",
            "msg_id": msg_id,
            "conversation_id": conversation_id,
            "history": [],
            "stream": False,
            "enable_thinking": False
        }
        
        if endpoint in ["/rag-qa", "/data-qa", "/all-qa"]:
            base_payload.update({
                "top_k": 10,
                "top_r": 5,
                "min_score": 0.3,
                "mode": "common"
            })
        
        if endpoint == "/search":
            return {
                "query": "PCB设计基础知识",
                "user_id": "perf_test_user",
                "msg_id": msg_id,
                "top_k": 10,
                "top_r": 5,
                "min_score": 0.3
            }
        
        return base_payload

    async def run_single_endpoint_test(self, endpoint: str, duration_seconds: int = 60,
                                     concurrent_requests: int = 10) -> List[PerformanceMetrics]:
        """运行单个端点的性能测试"""
        print(f"开始测试端点: {endpoint}")
        print(f"并发数: {concurrent_requests}, 持续时间: {duration_seconds}秒")

        end_time = time.time() + duration_seconds
        tasks = []

        async def worker():
            """工作协程"""
            metrics = []
            while time.time() < end_time:
                payload = self.generate_test_payload(endpoint)
                metric = await self.make_request(endpoint, payload)
                metrics.append(metric)
                self.monitor.add_performance_metric(metric)

                # 简单的速率控制
                await asyncio.sleep(0.1)
            return metrics

        # 启动并发工作协程
        for _ in range(concurrent_requests):
            tasks.append(asyncio.create_task(worker()))

        # 等待所有任务完成
        results = await asyncio.gather(*tasks)

        # 合并结果
        all_metrics = []
        for result in results:
            all_metrics.extend(result)

        print(f"端点 {endpoint} 测试完成，共发送 {len(all_metrics)} 个请求")
        return all_metrics

    async def run_comprehensive_test(self, test_config: Dict[str, Any]) -> TestSummary:
        """运行综合性能测试"""
        test_name = test_config.get("name", "comprehensive_test")
        endpoints = test_config.get("endpoints", ["/llm-qa", "/rag-qa", "/data-qa"])
        duration = test_config.get("duration_seconds", 300)
        concurrency = test_config.get("concurrent_requests", 20)

        print(f"=== 开始综合性能测试: {test_name} ===")
        print(f"测试端点: {endpoints}")
        print(f"总持续时间: {duration}秒")
        print(f"并发数: {concurrency}")

        # 开始系统监控
        self.monitor.start_monitoring(interval=1.0)

        try:
            # 为每个端点分配时间和并发数
            endpoint_duration = duration // len(endpoints)
            endpoint_concurrency = max(1, concurrency // len(endpoints))

            for endpoint in endpoints:
                await self.run_single_endpoint_test(
                    endpoint,
                    endpoint_duration,
                    endpoint_concurrency
                )

                # 端点间短暂休息
                await asyncio.sleep(2)

        finally:
            # 停止系统监控
            self.monitor.stop_monitoring()

        # 生成测试摘要
        summary = self.monitor.generate_summary(test_name)

        print(f"=== 测试完成 ===")
        print(f"总请求数: {summary.total_requests}")
        print(f"成功率: {(1-summary.error_rate)*100:.2f}%")
        print(f"平均响应时间: {summary.avg_response_time:.2f}ms")
        print(f"P95响应时间: {summary.p95_response_time:.2f}ms")
        print(f"RPS: {summary.requests_per_second:.2f}")

        return summary

# 预定义的测试配置
TEST_CONFIGURATIONS = {
    "quick": {
        "name": "快速测试",
        "endpoints": ["/health", "/llm-qa"],
        "duration_seconds": 60,
        "concurrent_requests": 5
    },
    "standard": {
        "name": "标准测试",
        "endpoints": ["/llm-qa", "/rag-qa", "/data-qa"],
        "duration_seconds": 300,
        "concurrent_requests": 20
    },
    "comprehensive": {
        "name": "综合测试",
        "endpoints": ["/llm-qa", "/rag-qa", "/data-qa", "/all-qa", "/search"],
        "duration_seconds": 600,
        "concurrent_requests": 50
    },
    "stress": {
        "name": "压力测试",
        "endpoints": ["/llm-qa", "/rag-qa", "/data-qa"],
        "duration_seconds": 900,
        "concurrent_requests": 100
    }
}

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="API性能测试工具")
    parser.add_argument("--url", default="http://localhost:8080/api/v1",
                       help="API基础URL")
    parser.add_argument("--token", default="Bear ipd-OOabxNh6usxsPgTt6EYZHqE1",
                       help="API访问令牌")
    parser.add_argument("--config", choices=list(TEST_CONFIGURATIONS.keys()),
                       default="standard", help="测试配置")
    parser.add_argument("--output", default="results", help="结果输出目录")
    parser.add_argument("--endpoint", help="测试单个端点")
    parser.add_argument("--duration", type=int, default=60, help="测试持续时间(秒)")
    parser.add_argument("--concurrency", type=int, default=10, help="并发请求数")

    args = parser.parse_args()

    # 创建输出目录
    Path(args.output).mkdir(exist_ok=True)

    async with APIPerformanceTester(args.url, args.token) as tester:
        if args.endpoint:
            # 单端点测试
            print(f"开始单端点性能测试: {args.endpoint}")
            tester.monitor.start_monitoring()

            try:
                await tester.run_single_endpoint_test(
                    args.endpoint,
                    args.duration,
                    args.concurrency
                )
            finally:
                tester.monitor.stop_monitoring()

            summary = tester.monitor.generate_summary(f"single_endpoint_{args.endpoint.replace('/', '_')}")
            tester.monitor.save_results(args.output, summary.test_name)

        else:
            # 综合测试
            config = TEST_CONFIGURATIONS[args.config]
            summary = await tester.run_comprehensive_test(config)
            tester.monitor.save_results(args.output, summary.test_name)

if __name__ == "__main__":
    asyncio.run(main())
