# API性能测试工具套件

这是一套完整的API性能测试工具，包括压力测试、性能监控、结果分析和报告生成功能。

## 🚀 功能特性

- **多种压测方式**: 支持Locust和自定义异步压测
- **实时监控**: 系统资源监控（CPU、内存、磁盘、网络）
- **详细分析**: 响应时间、吞吐量、错误率等指标分析
- **可视化报告**: 自动生成图表和HTML报告
- **灵活配置**: 支持多种测试场景和参数配置

## 📁 目录结构

```
tests/
├── load_test/              # Locust压测
│   ├── locustfile.py       # Locust测试脚本
│   ├── load_test_config.py # 压测配置
│   └── run_load_test.py    # 压测启动脚本
├── performance/            # 性能测试
│   └── performance_test.py # 异步性能测试脚本
├── data_generator/         # 测试数据生成
│   └── test_data_generator.py # 测试数据生成器
├── monitoring/             # 监控和分析
│   ├── system_monitor.py   # 系统资源监控
│   └── performance_analyzer.py # 性能分析工具
├── reports/                # 报告生成
│   └── report_template.py  # HTML报告模板
└── run_comprehensive_test.py # 综合测试工具
```

## 🛠️ 安装依赖

```bash
# 安装Python依赖
pip install locust aiohttp psutil pandas matplotlib seaborn

# 或使用requirements文件
pip install -r requirements.txt
```

## 📋 快速开始

### 1. 基础健康检查

```bash
# 检查API服务状态
python tests/load_test/run_load_test.py health --environment local
```

### 2. 运行简单压测

```bash
# 冒烟测试（5用户，2分钟）
python tests/load_test/run_load_test.py run smoke --environment local

# 标准负载测试（50用户，10分钟）
python tests/load_test/run_load_test.py run load --environment local
```

### 3. 运行综合测试

```bash
# 运行完整的性能测试套件
python tests/run_comprehensive_test.py --duration 300 --concurrency 20

# 使用配置文件
python tests/run_comprehensive_test.py --config test_config.json
```

## 🔧 详细使用说明

### Locust压测

#### 基本命令

```bash
# 列出可用场景
python tests/load_test/run_load_test.py list

# 运行特定场景
python tests/load_test/run_load_test.py run stress --environment local

# 交互式测试（带Web界面）
python tests/load_test/run_load_test.py run interactive
```

#### 自定义参数

```bash
# 使用自定义参数
python tests/load_test/run_load_test.py run load --args "--users 100 --spawn-rate 10"
```

### 性能测试

#### 单端点测试

```bash
# 测试单个端点
python tests/performance/performance_test.py --endpoint /llm-qa --duration 60 --concurrency 10
```

#### 综合测试

```bash
# 标准综合测试
python tests/performance/performance_test.py --config standard

# 压力测试
python tests/performance/performance_test.py --config stress
```

### 系统监控

```bash
# 实时监控系统资源
python tests/monitoring/system_monitor.py --interval 1 --duration 300

# 静默监控并保存结果
python tests/monitoring/system_monitor.py --quiet --duration 600 --output monitoring_results
```

### 结果分析

```bash
# 分析性能测试结果
python tests/monitoring/performance_analyzer.py \
  --performance-data results/performance.json \
  --system-data results/system.json \
  --charts --report
```

### 报告生成

```bash
# 生成HTML报告
python tests/reports/report_template.py \
  --test-summary results/summary.json \
  --performance-analysis results/analysis.json \
  --charts-dir results/charts \
  --output final_report.html

# 生成示例报告
python tests/reports/report_template.py --sample
```

## ⚙️ 配置说明

### 测试场景配置

预定义的测试场景：

- **smoke**: 冒烟测试（5用户，2分钟）
- **load**: 负载测试（50用户，10分钟）
- **stress**: 压力测试（200用户，15分钟）
- **spike**: 峰值测试（500用户，5分钟）
- **endurance**: 持久性测试（100用户，1小时）

### 环境配置

支持的环境：

- **local**: 本地开发环境
- **dev**: 开发环境
- **staging**: 预发布环境
- **prod**: 生产环境

### 自定义配置文件

```json
{
  "test_name": "API压力测试",
  "api_url": "http://localhost:8080/api/v1",
  "api_token": "Bear your-token-here",
  "test_config": {
    "endpoints": ["/llm-qa", "/rag-qa", "/data-qa"],
    "duration_seconds": 300,
    "concurrent_requests": 20
  },
  "locust_config": {
    "users": 50,
    "spawn_rate": 5,
    "run_time": "10m"
  }
}
```

## 📊 测试指标

### 性能指标

- **响应时间**: 平均值、中位数、P95、P99
- **吞吐量**: RPS（每秒请求数）
- **错误率**: 失败请求比例
- **并发性能**: 不同并发级别下的表现

### 系统指标

- **CPU使用率**: 平均值、峰值
- **内存使用率**: 平均值、峰值
- **磁盘I/O**: 读写速率
- **网络I/O**: 发送/接收速率

## 📈 报告示例

生成的HTML报告包含：

1. **测试摘要**: 总体统计信息
2. **端点分析**: 各API端点的详细性能数据
3. **响应时间分析**: 时间分布和趋势图表
4. **吞吐量分析**: RPS时间线图表
5. **错误分析**: 错误率和错误类型统计
6. **系统资源**: 服务器资源使用情况

## 🔍 故障排查

### 常见问题

1. **连接失败**
   ```bash
   # 检查API服务状态
   curl http://localhost:8080/api/v1/health
   ```

2. **认证失败**
   ```bash
   # 检查API令牌
   export API_ACCESS_TOKEN="your-token"
   ```

3. **依赖缺失**
   ```bash
   # 安装缺失的依赖
   pip install -r requirements.txt
   ```

### 性能调优建议

1. **并发数设置**: 根据服务器性能调整并发数
2. **测试时长**: 确保测试时间足够长以获得稳定结果
3. **监控间隔**: 根据需要调整监控采样频率
4. **资源限制**: 注意客户端和服务器的资源限制

## 📝 最佳实践

1. **测试前准备**
   - 确保测试环境稳定
   - 清理历史数据和日志
   - 预热服务（发送少量请求）

2. **测试执行**
   - 从小并发开始逐步增加
   - 监控系统资源使用情况
   - 记录测试环境和配置

3. **结果分析**
   - 关注P95和P99响应时间
   - 分析错误模式和原因
   - 对比不同测试场景的结果

4. **报告生成**
   - 包含测试环境信息
   - 添加结论和建议
   - 保存测试数据用于对比

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个工具套件。

## 📄 许可证

MIT License
